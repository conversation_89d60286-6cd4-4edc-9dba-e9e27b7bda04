#!/usr/bin/env python3
"""
企业数据语义匹配系统主程序

提供命令行界面用于构建索引和执行语义搜索
"""

import os
import sys
import time
import click
import json
from pathlib import Path
from typing import Optional

# 解决OpenMP冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.enhanced_search_engine import EnhancedSemanticSearchEngine
from src.utils import load_config


@click.group()
@click.option('--config', '-c', default='config.yaml', help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
@click.pass_context
def cli(ctx, config, verbose):
    """企业数据语义匹配系统"""
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config
    ctx.obj['verbose'] = verbose
    
    # 验证配置文件
    if not os.path.exists(config):
        click.echo(f"错误: 配置文件 {config} 不存在", err=True)
        sys.exit(1)


@cli.command()
@click.option('--force', '-f', is_flag=True, help='强制重建索引')
@click.pass_context
def build_index(ctx, force):
    """构建向量索引"""
    config_path = ctx.obj['config_path']
    
    click.echo("🚀 开始构建语义搜索索引...")
    
    try:
        # 初始化增强搜索引擎
        engine = EnhancedSemanticSearchEngine(config_path)

        # 如果强制重建，先清理缓存
        if force:
            import shutil
            cache_dir = engine.config['vector_storage']['cache_dir']
            if os.path.exists(cache_dir):
                click.echo("🗑️ 清理旧缓存...")
                shutil.rmtree(cache_dir)
                os.makedirs(cache_dir, exist_ok=True)

        # 构建索引
        if engine.initialize():
            # 显示状态信息
            status = engine.get_system_status()
            click.echo("\n✅ 索引构建完成!")
            click.echo(f"📊 企业记录数: {status['total_records']}")
            click.echo(f"🔢 向量维度: {status['vector_dimensions']}")
            click.echo(f"💾 索引类型: {status['index_types']}")
            click.echo(f"🔧 搜索模式: {status['mode']}")
        else:
            click.echo("❌ 索引构建失败", err=True)
            sys.exit(1)

    except Exception as e:
        click.echo(f"❌ 索引构建失败: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('query')
@click.option('--top-k', '-k', default=10, help='返回结果数量')
@click.option('--threshold', '-t', default=None, help='相似度阈值')
@click.option('--strategy', default='auto',
              type=click.Choice(['auto', 'primary_only', 'weighted_fusion', 'cascade']),
              help='搜索策略')
@click.option('--field-hints', help='字段提示（逗号分隔）')
@click.option('--output', '-o', help='输出文件路径')
@click.option('--format', '-f', default='table',
              type=click.Choice(['table', 'json', 'csv']), help='输出格式')
@click.pass_context
def search(ctx, query, top_k, threshold, strategy, field_hints, output, format):
    """执行多字段语义搜索"""
    config_path = ctx.obj['config_path']

    click.echo(f"🔍 搜索: '{query}'")
    if strategy != 'auto':
        click.echo(f"📋 策略: {strategy}")
    if field_hints:
        click.echo(f"🎯 字段提示: {field_hints}")

    try:
        # 初始化增强搜索引擎
        engine = EnhancedSemanticSearchEngine(config_path)

        # 初始化系统
        if not engine.initialize():
            click.echo("❌ 系统初始化失败，请先运行 'python main.py build-index'", err=True)
            sys.exit(1)

        # 处理字段提示
        field_hints_list = None
        if field_hints:
            field_hints_list = [hint.strip() for hint in field_hints.split(',')]

        # 执行搜索
        results = engine.search(
            query=query,
            top_k=top_k,
            threshold=threshold,
            search_strategy=strategy,
            field_hints=field_hints_list
        )

        if not results:
            click.echo("😔 未找到匹配的企业记录")
            return

        # 输出结果
        if format == 'table':
            _display_results_table(results)
        elif format == 'json':
            _display_results_json(results, output)
        elif format == 'csv':
            _export_results_csv(engine, results, output)

    except Exception as e:
        click.echo(f"❌ 搜索失败: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--queries-file', '-f', required=True, help='查询文件路径（每行一个查询）')
@click.option('--top-k', '-k', default=10, help='每个查询的返回结果数量')
@click.option('--threshold', '-t', default=None, help='相似度阈值')
@click.option('--output', '-o', required=True, help='输出文件路径')
@click.pass_context
def batch_search(ctx, queries_file, top_k, threshold, output):
    """批量搜索"""
    config_path = ctx.obj['config_path']
    
    if not os.path.exists(queries_file):
        click.echo(f"❌ 查询文件 {queries_file} 不存在", err=True)
        sys.exit(1)
    
    try:
        # 读取查询
        with open(queries_file, 'r', encoding='utf-8') as f:
            queries = [line.strip() for line in f if line.strip()]
        
        click.echo(f"📋 批量搜索 {len(queries)} 个查询...")

        # 初始化增强搜索引擎
        engine = EnhancedSemanticSearchEngine(config_path)

        # 初始化系统
        if not engine.initialize():
            click.echo("❌ 系统初始化失败，请先运行 'python main.py build-index'", err=True)
            sys.exit(1)

        # 执行批量搜索
        all_results = []
        for query in queries:
            results = engine.search(query, top_k=top_k, threshold=threshold)
            # 为每个结果添加查询信息
            for result in results:
                result['query'] = query
            all_results.append(results)
        
        # 导出结果
        _export_batch_results(all_results, output)
        click.echo(f"✅ 批量搜索完成，结果已保存到 {output}")
        
    except Exception as e:
        click.echo(f"❌ 批量搜索失败: {e}", err=True)
        sys.exit(1)



@cli.command()
@click.pass_context
def status(ctx):
    """显示系统状态"""
    config_path = ctx.obj['config_path']
    
    try:
        config = load_config(config_path)
        
        # 检查文件状态
        data_file = config['data']['input_file']
        model_path = config['model']['path']
        cache_dir = config['vector_storage']['cache_dir']

        # 检查多向量索引文件
        multi_index_files = [
            'multi_index_primary.faiss',
            'multi_index_status.faiss',
            'multi_index_date.faiss',
            'multi_index_identifier.faiss'
        ]

        click.echo("📊 系统状态:")
        click.echo(f"  📁 数据文件: {_check_file_status(data_file)}")
        click.echo(f"  🤖 模型目录: {_check_file_status(model_path)}")
        click.echo(f"  � 缓存目录: {_check_file_status(cache_dir)}")

        # 检查多向量索引文件状态
        click.echo("  � 多向量索引文件:")
        all_indexes_exist = True
        for index_file in multi_index_files:
            index_path = os.path.join(cache_dir, index_file)
            status = _check_file_status(index_path)
            click.echo(f"    - {index_file}: {status}")
            if not os.path.exists(index_path):
                all_indexes_exist = False

        # 如果所有索引都存在，显示详细信息
        if all_indexes_exist:
            try:
                engine = EnhancedSemanticSearchEngine(config_path)
                if engine.initialize():
                    status = engine.get_system_status()

                    click.echo("\n🔍 系统详细信息:")
                    click.echo(f"  📊 总记录数: {status['total_records']}")
                    click.echo(f"  � 搜索模式: {status['mode']}")
                    click.echo(f"  �🔢 向量维度: {status['vector_dimensions']}")
                    click.echo(f"  🏗️ 索引类型: {status['index_types']}")
                else:
                    click.echo("\n⚠️ 无法加载索引详细信息")
            except Exception as e:
                click.echo(f"\n⚠️ 获取索引信息失败: {e}")
        
    except Exception as e:
        click.echo(f"❌ 获取状态失败: {e}", err=True)


def _check_file_status(path: str) -> str:
    """检查文件状态"""
    if os.path.exists(path):
        if os.path.isfile(path):
            size_mb = os.path.getsize(path) / (1024 * 1024)
            return f"✅ {path} ({size_mb:.1f} MB)"
        else:
            return f"✅ {path} (目录)"
    else:
        return f"❌ {path} (不存在)"


def _display_results_table(results):
    """以表格形式显示结果"""
    click.echo(f"\n🎯 找到 {len(results)} 个匹配结果:\n")

    for i, result in enumerate(results, 1):
        click.echo(f"【{i}】 相似度: {result['similarity']:.3f}")
        click.echo(f"    企业名称: {result.get('company_name', '未知')}")
        click.echo(f"    地址: {result.get('address', '未知')}")

        # 显示法定代表人（多字段新增）
        if 'legal_representative' in result:
            click.echo(f"    法定代表人: {result['legal_representative']}")

        # 显示其他关键信息
        if 'company_record' in result:
            record = result['company_record']
            if '统一社会信用代码' in record:
                click.echo(f"    信用代码: {record['统一社会信用代码']}")
            if '登记状态' in record:
                click.echo(f"    登记状态: {record['登记状态']}")
            if '成立日期' in record:
                click.echo(f"    成立日期: {record['成立日期']}")
            if '经营范围' in record:
                scope = record['经营范围']
                if len(scope) > 100:
                    scope = scope[:100] + "..."
                click.echo(f"    经营范围: {scope}")

        click.echo()


def _display_results_json(results, output_file: Optional[str] = None):
    """以JSON格式显示结果"""
    json_data = json.dumps(results, ensure_ascii=False, indent=2)
    
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(json_data)
        click.echo(f"✅ 结果已保存到 {output_file}")
    else:
        click.echo(json_data)


def _export_results_csv(engine, results, output_file: Optional[str] = None):
    """导出CSV格式结果"""
    if not output_file:
        output_file = f"search_results_{int(time.time())}.csv"

    try:
        # 使用增强搜索引擎的导出功能
        if hasattr(engine, 'export_results'):
            engine.export_results(results, output_file, format='csv')
        else:
            # 备用导出方法
            import pandas as pd
            export_data = []
            for result in results:
                row = {
                    'similarity': result['similarity'],
                    'company_name': result.get('company_name', ''),
                    'address': result.get('address', ''),
                    'legal_representative': result.get('legal_representative', ''),
                }
                if 'company_record' in result:
                    row.update(result['company_record'])
                export_data.append(row)

            df = pd.DataFrame(export_data)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')

        click.echo(f"✅ 结果已导出到 {output_file}")
    except Exception as e:
        click.echo(f"❌ 导出失败: {e}", err=True)


def _export_batch_results(all_results, output_file: str):
    """导出批量搜索结果"""
    import pandas as pd
    
    # 合并所有结果
    combined_results = []
    for query_results in all_results:
        combined_results.extend(query_results)
    
    if not combined_results:
        click.echo("⚠️ 没有找到任何匹配结果")
        return
    
    # 准备导出数据
    export_data = []
    for result in combined_results:
        row = {
            'query': result['query'],
            'similarity': result['similarity'],
            'address': result['address'],
            **result['company_record']
        }
        export_data.append(row)
    
    # 导出到Excel
    df = pd.DataFrame(export_data)
    df.to_excel(output_file, index=False)


if __name__ == '__main__':
    cli()
