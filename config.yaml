# Semantic Matching System Configuration

# Model Configuration
model:
  name: "bge-m3"
  path: "./bge-m3"  # Local model path
  max_length: 512
  batch_size: 32
  device: "cuda"  # Use "cuda" if GPU available

# Data Configuration
data:
  input_file: "./data/浙江所有企业1.xlsx"
  encoding: "utf-8"
  # 多字段向量化配置
  multi_field:
    enabled: true
    # 主要字段及其权重（用于主向量）
    primary_fields:
      公司名称: 0.4
      最新年报地址: 0.4
      法定代表人: 0.2
    # 辅助字段（用于辅助向量）
    auxiliary_fields:
      status: "登记状态"
      date: "成立日期"
      identifier: ["纳税人识别号", "有效手机号"]
  
# Vector Storage Configuration
vector_storage:
  cache_dir: "./cache"
  vectors_file: "company_vectors.npy"
  metadata_file: "company_metadata.pkl"
  
# FAISS Index Configuration
faiss:
  index_type: "FLAT"
  index_file: "company_index.faiss"
  hnsw_m: 16  # Number of connections for HNSW
  hnsw_ef_construction: 200  # Size of dynamic candidate list
  hnsw_ef_search: 100  # Size of dynamic candidate list during search
  
# Search Configuration
search:
  similarity_threshold: 0.3
  max_results: 10
  normalize_vectors: true
  similarity_metric: "cosine"  # cosine, euclidean, inner_product
  
# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
  file: "./logs/semantic_search.log"
