"""
数据处理模块

负责加载、清洗和预处理企业数据，提取地址字段用于向量化
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
from loguru import logger
from .utils import clean_text, validate_file_exists


class DataProcessor:
    """企业数据处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据处理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})
        self.input_file = self.data_config.get('input_file')

        # 多字段向量化配置
        self.multi_field_config = self.data_config.get('multi_field', {
            'enabled': True,
            'primary_fields': {
                '公司名称': 0.4,
                '最新年报地址': 0.4,
                '法定代表人': 0.2
            },
            'auxiliary_fields': {
                'status': '登记状态',
                'date': '成立日期',
                'identifier': ['纳税人识别号', '有效手机号']
            }
        })
        self.encoding = self.data_config.get('encoding', 'utf-8')
        
        self.df = None
        self.processed_data = None
        
    def load_data(self) -> pd.DataFrame:
        """
        加载企业数据文件
        
        Returns:
            pandas.DataFrame: 加载的数据
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式不支持或数据有问题
        """
        validate_file_exists(self.input_file, "Data file")
        
        file_path = Path(self.input_file)
        file_ext = file_path.suffix.lower()
        
        logger.info(f"Loading data from {self.input_file}")
        
        try:
            if file_ext in ['.xlsx', '.xls']:
                self.df = pd.read_excel(self.input_file)
            elif file_ext == '.csv':
                self.df = pd.read_csv(self.input_file, encoding=self.encoding)
            else:
                raise ValueError(f"Unsupported file format: {file_ext}")
                
            logger.info(f"Data loaded successfully: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
            logger.info(f"Columns: {list(self.df.columns)}")
            
            return self.df
            
        except Exception as e:
            logger.error(f"Failed to load data: {e}")
            raise
    
    def validate_data(self) -> None:
        """
        验证数据完整性

        Raises:
            ValueError: 数据验证失败
        """
        if self.df is None:
            raise ValueError("Data not loaded. Call load_data() first.")

        # 验证多字段配置中的必需字段
        primary_fields = self.multi_field_config.get('primary_fields', {})
        missing_fields = []

        for field_name in primary_fields.keys():
            if field_name not in self.df.columns:
                missing_fields.append(field_name)

        if missing_fields:
            available_cols = list(self.df.columns)
            raise ValueError(
                f"Required primary fields not found: {missing_fields}. "
                f"Available columns: {available_cols}"
            )

        # 检查主要字段的数据质量
        total_count = len(self.df)
        logger.info(f"Data validation:")
        logger.info(f"  - Total records: {total_count}")

        for field_name in primary_fields.keys():
            null_count = self.df[field_name].isnull().sum()
            null_ratio = null_count / total_count
            logger.info(f"  - {field_name}: {null_count} nulls ({null_ratio:.2%})")

            if null_ratio > 0.5:
                logger.warning(f"High null ratio in {field_name}: {null_ratio:.2%}")
    
    def clean_addresses(self) -> List[str]:
        """
        清洗地址数据（向后兼容方法，现在使用多字段处理）

        Returns:
            List[str]: 清洗后的地址列表
        """
        if self.df is None:
            raise ValueError("Data not loaded. Call load_data() first.")

        logger.info("Cleaning address data...")

        # 从多字段配置中获取地址字段
        address_field = None
        for field_name in self.multi_field_config.get('primary_fields', {}).keys():
            if '地址' in field_name:
                address_field = field_name
                break

        if not address_field:
            raise ValueError("No address field found in primary_fields configuration")

        # 提取地址列并处理空值
        addresses = self.df[address_field].fillna('').astype(str)

        # 清洗每个地址
        cleaned_addresses = []
        for addr in addresses:
            cleaned_addr = clean_text(addr)
            cleaned_addresses.append(cleaned_addr)

        # 统计清洗结果
        empty_count = sum(1 for addr in cleaned_addresses if not addr.strip())
        valid_count = len(cleaned_addresses) - empty_count

        logger.info(f"Address cleaning completed:")
        logger.info(f"  - Valid addresses: {valid_count}")
        logger.info(f"  - Empty addresses: {empty_count}")

        return cleaned_addresses
    
    def process_data(self) -> Tuple[List[str], pd.DataFrame]:
        """
        处理完整数据流程
        
        Returns:
            Tuple[List[str], pd.DataFrame]: (清洗后的地址列表, 原始数据框)
        """
        # 加载数据
        self.load_data()
        
        # 验证数据
        self.validate_data()
        
        # 清洗地址
        addresses = self.clean_addresses()
        
        # 创建处理后的数据结构
        self.processed_data = {
            'addresses': addresses,
            'dataframe': self.df.copy(),
            'metadata': {
                'total_records': len(self.df),
                'valid_addresses': sum(1 for addr in addresses if addr.strip()),
                'columns': list(self.df.columns),
                'multi_field_config': self.multi_field_config
            }
        }
        
        logger.info("Data processing completed successfully")
        return addresses, self.df
    
    def get_record_by_index(self, index: int) -> Dict[str, Any]:
        """
        根据索引获取完整记录
        
        Args:
            index: 记录索引
            
        Returns:
            Dict[str, Any]: 完整的企业记录
        """
        if self.df is None:
            raise ValueError("Data not loaded")
            
        if index < 0 or index >= len(self.df):
            raise IndexError(f"Index {index} out of range [0, {len(self.df)})")
            
        record = self.df.iloc[index].to_dict()
        return record
    
    def get_records_by_indices(self, indices: List[int]) -> List[Dict[str, Any]]:
        """
        根据索引列表获取多个完整记录
        
        Args:
            indices: 记录索引列表
            
        Returns:
            List[Dict[str, Any]]: 完整的企业记录列表
        """
        records = []
        for idx in indices:
            try:
                record = self.get_record_by_index(idx)
                records.append(record)
            except IndexError:
                logger.warning(f"Index {idx} out of range, skipping")
                continue
        return records
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取数据处理摘要
        
        Returns:
            Dict[str, Any]: 数据摘要信息
        """
        if self.processed_data is None:
            return {"status": "not_processed"}
            
        return {
            "status": "processed",
            "metadata": self.processed_data['metadata'],
            "sample_addresses": self.processed_data['addresses'][:5],
            "sample_records": [
                self.get_record_by_index(i)
                for i in range(min(3, len(self.df)))
            ]
        }

    def get_multi_field_texts(self) -> Dict[str, List[str]]:
        """
        获取多字段文本数据

        Returns:
            Dict[str, List[str]]: 包含各字段文本列表的字典
        """
        if self.df is None:
            raise RuntimeError("Data not loaded. Call load_data() first.")

        result = {}

        # 主要字段
        for field in self.multi_field_config['primary_fields'].keys():
            if field in self.df.columns:
                result[field] = self.df[field].fillna('').astype(str).tolist()
            else:
                logger.warning(f"Primary field '{field}' not found in data")
                result[field] = [''] * len(self.df)

        # 辅助字段
        aux_fields = self.multi_field_config['auxiliary_fields']
        for field_type, field_names in aux_fields.items():
            if isinstance(field_names, list):
                # 多个字段组合
                combined_texts = []
                for idx in range(len(self.df)):
                    field_values = []
                    for field_name in field_names:
                        if field_name in self.df.columns:
                            value = str(self.df[field_name].iloc[idx])
                            if value and value != 'nan':
                                field_values.append(value)
                    combined_texts.append(' '.join(field_values))
                result[field_type] = combined_texts
            else:
                # 单个字段
                if field_names in self.df.columns:
                    result[field_type] = self.df[field_names].fillna('').astype(str).tolist()
                else:
                    logger.warning(f"Auxiliary field '{field_names}' not found in data")
                    result[field_type] = [''] * len(self.df)

        return result

    def create_primary_vector_text(self, record_index: int) -> str:
        """
        为指定记录创建主向量文本（加权组合）

        Args:
            record_index: 记录索引

        Returns:
            str: 组合后的文本
        """
        if self.df is None:
            raise RuntimeError("Data not loaded")

        if record_index < 0 or record_index >= len(self.df):
            raise IndexError(f"Record index {record_index} out of range")

        record = self.df.iloc[record_index]
        primary_fields = self.multi_field_config['primary_fields']

        # 根据权重组合字段
        weighted_texts = []
        for field, weight in primary_fields.items():
            if field in self.df.columns:
                value = str(record[field])
                if value and value != 'nan':
                    # 根据权重重复文本（简单的权重实现）
                    repeat_count = max(1, int(weight * 10))
                    weighted_texts.extend([value] * repeat_count)

        return ' '.join(weighted_texts)

    def get_primary_vector_texts(self) -> List[str]:
        """
        获取所有记录的主向量文本

        Returns:
            List[str]: 主向量文本列表
        """
        if self.df is None:
            raise RuntimeError("Data not loaded")

        return [self.create_primary_vector_text(i) for i in range(len(self.df))]
