#!/usr/bin/env python3
"""
多向量索引构建器
支持分层向量化架构的索引管理
"""

import faiss
import numpy as np
import pickle
import os
from typing import Dict, List, Any, Optional, Tuple
import time

from .multi_vectorizer import VectorizedRecord
from .utils import setup_logger

logger = setup_logger(__name__)

class MultiVectorIndexBuilder:
    """
    多向量索引构建器
    管理主向量索引和辅助向量索引
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化多向量索引构建器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.faiss_config = config.get('faiss', {})
        self.multi_field_config = config.get('data', {}).get('multi_field', {})
        
        # 检查是否启用多字段向量化
        self.enabled = self.multi_field_config.get('enabled', False)
        
        # 索引配置
        self.index_type = self.faiss_config.get('index_type', 'FLAT')
        self.cache_dir = self.faiss_config.get('cache_dir', './cache')
        
        # 索引存储
        self.indexes = {}  # 存储各类向量的索引
        self.vector_records = []  # 存储向量化记录
        
        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)
        
        logger.info(f"Multi-vector index builder initialized (enabled: {self.enabled})")
    
    def build_indexes(self, vectorized_records: List[VectorizedRecord]) -> Dict[str, Any]:
        """
        构建多向量索引
        
        Args:
            vectorized_records: 向量化记录列表
            
        Returns:
            Dict[str, Any]: 索引构建结果
        """
        if not self.enabled:
            raise RuntimeError("Multi-field vectorization is not enabled")
        
        logger.info("Building multi-vector indexes...")
        start_time = time.time()
        
        self.vector_records = vectorized_records
        
        # 1. 构建主向量索引
        primary_vectors = np.array([record.primary_vector for record in vectorized_records])
        self.indexes['primary'] = self._build_single_index(primary_vectors, 'primary')
        
        # 2. 构建辅助向量索引
        auxiliary_field_types = set()
        for record in vectorized_records:
            auxiliary_field_types.update(record.auxiliary_vectors.keys())
        
        for field_type in auxiliary_field_types:
            aux_vectors = np.array([
                record.auxiliary_vectors.get(field_type, np.zeros(primary_vectors.shape[1]))
                for record in vectorized_records
            ])
            self.indexes[field_type] = self._build_single_index(aux_vectors, field_type)
        
        # 3. 保存索引和元数据
        self._save_indexes()
        self._save_metadata()
        
        elapsed_time = time.time() - start_time
        result = {
            'build_time': elapsed_time,
            'total_vectors': len(vectorized_records),
            'index_types': list(self.indexes.keys()),
            'primary_index_size': self.indexes['primary'].ntotal,
            'vector_dimension': primary_vectors.shape[1]
        }
        
        logger.info(f"Multi-vector indexes built in {elapsed_time:.2f}s")
        logger.info(f"Index types: {list(self.indexes.keys())}")
        
        return result
    
    def _build_single_index(self, vectors: np.ndarray, index_name: str) -> faiss.Index:
        """
        构建单个向量索引
        
        Args:
            vectors: 向量数组
            index_name: 索引名称
            
        Returns:
            faiss.Index: FAISS索引
        """
        dimension = vectors.shape[1]
        
        logger.info(f"Building {index_name} index: {vectors.shape[0]} vectors, {dimension} dimensions")
        
        # 根据配置选择索引类型
        similarity_metric = self.config.get('search', {}).get('similarity_metric', 'cosine')
        
        if self.index_type.upper() == 'FLAT':
            if similarity_metric == 'cosine' or similarity_metric == 'inner_product':
                index = faiss.IndexFlatIP(dimension)
            elif similarity_metric == 'euclidean':
                index = faiss.IndexFlatL2(dimension)
            else:
                logger.warning(f"Unknown similarity metric: {similarity_metric}, using cosine")
                index = faiss.IndexFlatIP(dimension)
        else:
            # 其他索引类型的实现
            logger.warning(f"Index type {self.index_type} not fully implemented, using FLAT")
            index = faiss.IndexFlatIP(dimension)
        
        # 添加向量到索引
        index.add(vectors.astype(np.float32))
        
        logger.info(f"{index_name} index built successfully: {index.ntotal} vectors")
        return index
    
    def search_multi_vector(
        self, 
        query_vectors: Dict[str, np.ndarray],
        k: int = 10,
        search_strategy: str = 'primary_only'
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        多向量搜索
        
        Args:
            query_vectors: 查询向量字典
            k: 返回结果数量
            search_strategy: 搜索策略
                - 'primary_only': 仅使用主向量
                - 'weighted_fusion': 加权融合多个向量
                - 'cascade': 级联搜索
                
        Returns:
            Tuple[np.ndarray, np.ndarray]: (scores, indices)
        """
        if not self.enabled or 'primary' not in self.indexes:
            raise RuntimeError("Multi-vector indexes not available")
        
        if search_strategy == 'primary_only':
            return self._search_primary_only(query_vectors, k)
        elif search_strategy == 'weighted_fusion':
            return self._search_weighted_fusion(query_vectors, k)
        elif search_strategy == 'cascade':
            return self._search_cascade(query_vectors, k)
        else:
            logger.warning(f"Unknown search strategy: {search_strategy}, using primary_only")
            return self._search_primary_only(query_vectors, k)
    
    def _search_primary_only(
        self, 
        query_vectors: Dict[str, np.ndarray], 
        k: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """仅使用主向量搜索"""
        if 'primary' not in query_vectors:
            raise ValueError("Primary query vector not provided")
        
        primary_query = query_vectors['primary'].reshape(1, -1).astype(np.float32)
        scores, indices = self.indexes['primary'].search(primary_query, k)
        
        return scores[0], indices[0]
    
    def _search_weighted_fusion(
        self, 
        query_vectors: Dict[str, np.ndarray], 
        k: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """加权融合多向量搜索"""
        # 搜索策略权重
        weights = {
            'primary': 0.7,
            'status': 0.1,
            'date': 0.1,
            'identifier': 0.1
        }
        
        all_scores = {}
        all_indices = {}
        
        # 对每个可用的向量类型进行搜索
        for vector_type, query_vector in query_vectors.items():
            if vector_type in self.indexes:
                query = query_vector.reshape(1, -1).astype(np.float32)
                scores, indices = self.indexes[vector_type].search(query, k * 2)  # 搜索更多结果用于融合
                all_scores[vector_type] = scores[0]
                all_indices[vector_type] = indices[0]
        
        # 加权融合结果
        return self._fuse_search_results(all_scores, all_indices, weights, k)
    
    def _search_cascade(
        self, 
        query_vectors: Dict[str, np.ndarray], 
        k: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """级联搜索：先用主向量粗筛，再用辅助向量精排"""
        # 第一阶段：主向量粗筛
        primary_scores, primary_indices = self._search_primary_only(query_vectors, k * 3)
        
        # 第二阶段：辅助向量精排
        if len(query_vectors) > 1:
            # 重新计算候选结果的辅助向量相似度
            refined_scores = []
            for idx in primary_indices:
                total_score = 0.0
                weight_sum = 0.0
                
                # 主向量权重
                primary_score = primary_scores[np.where(primary_indices == idx)[0][0]]
                total_score += primary_score * 0.7
                weight_sum += 0.7
                
                # 辅助向量权重
                for vector_type, query_vector in query_vectors.items():
                    if vector_type != 'primary' and vector_type in self.indexes:
                        # 计算与候选记录的相似度
                        candidate_vector = self._get_vector_by_index(vector_type, idx)
                        if candidate_vector is not None:
                            similarity = np.dot(query_vector, candidate_vector)
                            total_score += similarity * 0.1
                            weight_sum += 0.1
                
                refined_scores.append(total_score / weight_sum if weight_sum > 0 else total_score)
            
            # 重新排序
            refined_scores = np.array(refined_scores)
            sorted_indices = np.argsort(refined_scores)[::-1][:k]
            
            final_scores = refined_scores[sorted_indices]
            final_indices = primary_indices[sorted_indices]
            
            return final_scores, final_indices
        else:
            return primary_scores[:k], primary_indices[:k]
    
    def _fuse_search_results(
        self, 
        all_scores: Dict[str, np.ndarray],
        all_indices: Dict[str, np.ndarray],
        weights: Dict[str, float],
        k: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """融合多个搜索结果"""
        # 收集所有候选索引
        all_candidates = set()
        for indices in all_indices.values():
            all_candidates.update(indices)
        
        # 计算每个候选的加权分数
        candidate_scores = {}
        for candidate_idx in all_candidates:
            weighted_score = 0.0
            total_weight = 0.0
            
            for vector_type, indices in all_indices.items():
                if candidate_idx in indices:
                    pos = np.where(indices == candidate_idx)[0][0]
                    score = all_scores[vector_type][pos]
                    weight = weights.get(vector_type, 0.1)
                    
                    weighted_score += score * weight
                    total_weight += weight
            
            if total_weight > 0:
                candidate_scores[candidate_idx] = weighted_score / total_weight
        
        # 排序并返回top-k
        sorted_candidates = sorted(candidate_scores.items(), key=lambda x: x[1], reverse=True)[:k]
        
        final_scores = np.array([score for _, score in sorted_candidates])
        final_indices = np.array([idx for idx, _ in sorted_candidates])
        
        return final_scores, final_indices
    
    def _get_vector_by_index(self, vector_type: str, record_index: int) -> Optional[np.ndarray]:
        """根据索引获取向量"""
        if record_index < 0 or record_index >= len(self.vector_records):
            return None
        
        record = self.vector_records[record_index]
        
        if vector_type == 'primary':
            return record.primary_vector
        else:
            return record.auxiliary_vectors.get(vector_type)
    
    def _save_indexes(self) -> None:
        """保存所有索引"""
        for index_name, index in self.indexes.items():
            index_path = os.path.join(self.cache_dir, f'multi_index_{index_name}.faiss')
            faiss.write_index(index, index_path)
            logger.info(f"Saved {index_name} index to {index_path}")
    
    def _save_metadata(self) -> None:
        """保存元数据"""
        metadata = {
            'vector_records': self.vector_records,
            'config': self.config,
            'index_types': list(self.indexes.keys())
        }
        
        metadata_path = os.path.join(self.cache_dir, 'multi_vector_metadata.pkl')
        with open(metadata_path, 'wb') as f:
            pickle.dump(metadata, f)
        
        logger.info(f"Saved metadata to {metadata_path}")
    
    def load_indexes(self) -> bool:
        """加载已保存的索引"""
        try:
            # 加载元数据
            metadata_path = os.path.join(self.cache_dir, 'multi_vector_metadata.pkl')
            if not os.path.exists(metadata_path):
                return False
            
            with open(metadata_path, 'rb') as f:
                metadata = pickle.load(f)
            
            self.vector_records = metadata['vector_records']
            index_types = metadata['index_types']
            
            # 加载索引
            for index_name in index_types:
                index_path = os.path.join(self.cache_dir, f'multi_index_{index_name}.faiss')
                if os.path.exists(index_path):
                    self.indexes[index_name] = faiss.read_index(index_path)
                    logger.info(f"Loaded {index_name} index from {index_path}")
                else:
                    logger.warning(f"Index file not found: {index_path}")
                    return False
            
            logger.info("Multi-vector indexes loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load multi-vector indexes: {e}")
            return False
